import React from "react";
import { Layout, <PERSON>u, But<PERSON>, theme } from "antd";
import { BarChartOutlined } from "@ant-design/icons";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Archive,
  BookOpen,
  ChartBarStacked,
  Clock,
  ContactRound,
  GraduationCap,
  IdCard,
  Landmark,
  LayoutDashboard,
  LockKeyhole,
  Map,
  Package,
  Package2,
  Percent,
  RouteIcon,
  School,
  Spline,
  StopCircle,
  Store,
  Users,
  UserSearch,
  X,
  Calculator,
  Calendar,
  PackageXIcon,
  CreditCard,
  DollarSign,
  MapPin,
  Bus,
  Notebook,
  Car,
  IdCardIcon,
  Settings,
  GraduationCap as AcademicYearIcon,
  FileText,
  BarChart3,
} from "lucide-react";
import { assets } from "../../assets/assets.ts";
import { useTranslation } from "react-i18next";

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  isMobile: boolean;
}

const AuthSidebar: React.FC<SidebarProps> = ({
  collapsed,
  onCollapse,
  isMobile,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { token } = theme.useToken();
  const { t } = useTranslation();

  const permissions: string[] = JSON.parse(
    localStorage.getItem("PERMISSIONS") || "[]"
  );

  const allMenuItems: any[] = [
    {
      key: "/auth/admin-dashboard",
      icon: <LayoutDashboard className="w-5 h-5" />,
      label: t("auth_sidebar.dashboard"),
      permission: "manage_dashboard",
    },
      {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.manage_stats")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/statistiques",
      icon: <IdCardIcon className="w-5 h-5" />,
      label: t("auth_sidebar.categories.manage_stats"),
      //permission: 'manage_stats',
    },
    {
      key: "/auth/audits/statistics",
      icon: <BarChart3 className="w-5 h-5" />,
      label: t("auth_sidebar.audit_statistics"),
      //permission: "view_audit_statistics",
    },
    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.security")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/roles-permissions",
      icon: <LockKeyhole className="w-5 h-5" />,
      label: t("auth_sidebar.roles_permissions"),
      permission: "manage_roles_permissions",
    },
    {
      key: "/auth/manage-admins",
      icon: <UserSearch className="w-5 h-5" />,
      label: t("auth_sidebar.manage_admins"),
      permission: "manage_admins",
    },
    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.location")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/gouvernorats",
      icon: <Landmark className="w-5 h-5" />,
      label: t("auth_sidebar.manage_governorates"),
      permission: "manage_governorates",
    },
    {
      key: "/auth/delegations",
      icon: <Map className="w-5 h-5" />,
      label: t("auth_sidebar.manage_delegations"),
      permission: "manage_delegations",
    },
    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.education")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/type-etablissement",
      icon: <ChartBarStacked className="w-5 h-5" />,
      label: t("auth_sidebar.manage_establishmentTypes"),
      permission: "manage_establishment_types",
    },
    {
      key: "/auth/niveaux-scolaires",
      icon: <GraduationCap className="w-5 h-5" />,
      label: t("auth_sidebar.manage_schoolDegrees"),
      permission: "manage_school_degrees",
    },
    {
      key: "/auth/academic-years",
      icon: <AcademicYearIcon className="w-5 h-5" />,
      label: t("auth_sidebar.manage_academicYears"),
      permission: "manage_academic_years",
    },
    {
      key: "/auth/etablissements",
      icon: <School className="w-5 h-5" />,
      label: t("auth_sidebar.manage_establishment"),
      permission: "manage_establishments",
    },
    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.transport")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/saisons",
      icon: <Calendar className="w-5 h-5" />,
      label: t("auth_sidebar.manage_seasons"),
      permission: "manage_seasons",
    },
    {
      key: "/auth/stations",
      icon: <StopCircle className="w-5 h-5" />,
      label: t("auth_sidebar.manage_stations"),
      permission: "manage_stations",
    },
    {
      key: "/auth/trajets",
      icon: <Spline className="w-5 h-5" />,
      label: t("auth_sidebar.manage_routes"),
      permission: "manage_routes",
    },
    {
      key: "/auth/lignes",
      icon: <RouteIcon className="w-5 h-5" />,
      label: t("auth_sidebar.manage_lines"),
      permission: "manage_lines",
    },
    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.busLocations")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/type-vehicules",
      icon: <Bus className="w-5 h-5" />,
      label: t("auth_sidebar.manage_typeVehicules"),
      permission: "manage_type_vehicules",
    },
    {
      key: "/auth/location-types",
      icon: <MapPin className="w-5 h-5" />,
      label: t("auth_sidebar.manage_locationTypes"),
      permission: "manage_location_types",
    },
    {
      key: "/auth/location-seasons",
      icon: <Calendar className="w-5 h-5" />,
      label: t("auth_sidebar.manage_location_seasons"),
      permission: "manage_location_seasons",
    },
    {
      key: "/auth/vehicle-season-pricing",
      icon: <Notebook className="w-5 h-5" />,
      label: t("auth_sidebar.manage_vehicle_season_pricing"),
      permission: "manage_vehicle_season_pricing",
    },
    {
      key: "/auth/type-vehicle-type-locations",
      icon: <Car className="w-5 h-5" />,
      label: t("auth_sidebar.manage_typeVehicleTypeLocations"),
      permission: "manage_type_vehicle_type_locations",
    },
    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.pricing")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/types-cartes",
      icon: <Package className="w-5 h-5" />,
      label: t("auth_sidebar.manage_cardTypes"),
      permission: "manage_card_types",
    },
    {
      key: "/auth/types-abonnements",
      icon: <Package2 className="w-5 h-5" />,
      label: t("auth_sidebar.manage_abnTypes"),
      permission: "manage_abn_types",
    },
    {
      key: "/auth/methodes-paiements",
      icon: <CreditCard className="w-5 h-5" />,
      label: t("auth_sidebar.manage_paymentMethods"),
      permission: "manage_payment_methods",
    },
    {
      key: "/auth/motifs-duplication",
      icon: <X className="w-5 h-5" />,
      label: t("auth_sidebar.manage_duplicateMotifs"),
      permission: "manage_duplicate_motifs",
    },
    {
      key: "/auth/base-tarifaires",
      icon: <Calculator className="w-5 h-5" />,
      label: t("auth_sidebar.manage_tariffBases"),
      permission: "manage_tariff_bases",
    },
    {
      key: "/auth/frais-cartes",
      icon: <DollarSign className="w-5 h-5" />,
      label: t("auth_sidebar.manage_cardsFees"),
      permission: "manage_cards_fees",
    },
    {
      key: "/auth/remises",
      icon: <Percent className="w-5 h-5" />,
      label: t("auth_sidebar.manage_discounts"),
      permission: "manage_discounts",
    },

    {
      key: "/auth/configs",
      icon: <Settings className="w-5 h-5" />,
      label: t("auth_sidebar.manage_configs"),
      permission: "manage_configs",
    },

    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.sales")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/compagnes",
      icon: <BookOpen className="w-5 h-5" />,
      label: t("auth_sidebar.manage_campaigns"),
      permission: "manage_campaigns",
    },
    {
      key: "/auth/periode-vente",
      icon: <Clock className="w-5 h-5" />,
      label: t("auth_sidebar.manage_salesPeriods"),
      permission: "manage_sales_periods",
    },
    {
      key: "/auth/peiodicitees",
      icon: <Clock className="w-5 h-5" />,
      label: t("auth_sidebar.manage_abnPeriods"),
      permission: "manage_periodicities",
    },
    {
      key: "/auth/agences",
      icon: <Landmark className="w-5 h-5" />,
      label: t("auth_sidebar.manage_agencies"),
      permission: "manage_agencies",
    },
    {
      key: "/auth/points-ventes",
      icon: <Store className="w-5 h-5" />,
      label: t("auth_sidebar.manage_salesPoints"),
      permission: "manage_sales_points",
    },
    {
      key: "/auth/affectation-agents",
      icon: <Archive className="w-5 h-5" />,
      label: t("auth_sidebar.manage_assignAgents"),
      permission: "manage_assign_agents",
    },
    {
      key: "/auth/stock-cartes",
      icon: <PackageXIcon className="w-5 h-5" />,
      label: t("auth_sidebar.manage_stockCards"),
      permission: "manage_stock_cards",
    },
    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.subscriptions")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    /*
    {
      key: "/auth/types-clients",
      icon: <ContactRound className="w-5 h-5" />,
      label: t("auth_sidebar.manage_clientTypes"),
      permission: "manage_client_types",
    },
    {
      key: "/auth/clients",
      icon: <Users className="w-5 h-5" />,
      label: t("auth_sidebar.manage_clients"),
      permission: "manage_clients",
    },
    */
    {
      key: "/auth/abonnements",
      icon: <IdCardIcon className="w-5 h-5" />,
      label: t("auth_sidebar.manage_newSubs"),
      permission: "manage_newSubs",
    },
    {
      type: collapsed ? "divider" : "group",
      label: (
        <div className="flex items-center py-1">
          {!collapsed && (
            <>
              <span className="text-gray-400 uppercase text-xs tracking-wide  mx-2">
                {t("auth_sidebar.categories.system")}
              </span>
              <div className="flex-grow border-t border-gray-300"></div>
            </>
          )}
        </div>
      ),
    },
    {
      key: "/auth/audits",
      icon: <FileText className="w-5 h-5" />,
      label: t("auth_sidebar.manage_audits"),
      //permission: "manage_audits",
    },
  ];

  // Premier filtrage basé sur les permissions
  const filteredMenuItems: any[] = allMenuItems.filter((item: any) => {
    if (item.type === "divider" || item.type === "group") {
      return true;
    }
    if (item.permission) {
      return permissions.includes(item.permission);
    }
    return true;
  });

  // Deuxième filtrage pour supprimer les groupes vides
  const finalFilteredItems = filteredMenuItems.filter((item, index) => {
    if (item.type !== "divider" && item.type !== "group") {
      return true;
    }
    const nextDividerIndex = filteredMenuItems.findIndex(
      (nextItem, nextIndex) =>
        nextIndex > index &&
        (nextItem.type === "divider" || nextItem.type === "group")
    );
    const sectionEnd =
      nextDividerIndex === -1 ? filteredMenuItems.length : nextDividerIndex;
    const sectionItems = filteredMenuItems.slice(index + 1, sectionEnd);
    return sectionItems.some(
      (item) => item.type !== "divider" && item.type !== "group"
    );
  });

  if (isMobile && !collapsed) {
    return (
      <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
        <div
          className="fixed inset-y-0 left-0 w-64 bg-white"
          style={{
            boxShadow: token.boxShadow,
            transition: `all ${token.motionDurationMid}`,
          }}
        >
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center justify-content-center gap-8">
                <span
                  className="text-xl font-bold"
                  style={{ color: token.colorTextHeading }}
                >
                  <img src={assets.logoSrtgn} alt="logo" width={120} />
                </span>
              </div>
              <Button
                type="text"
                icon={<X className="w-5 h-5" />}
                onClick={() => onCollapse(true)}
                className="flex items-center justify-center"
              />
            </div>
          </div>
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={finalFilteredItems}
            onClick={({ key }) => {
              navigate(key);
              if (isMobile) onCollapse(true);
            }}
            style={{ borderRight: "none" }}
          />
        </div>
      </div>
    );
  }

  return (
    <Sider
      width={260}
      collapsible
      collapsed={collapsed}
      onCollapse={onCollapse}
      trigger={null}
      collapsedWidth={isMobile ? 0 : 80}
      className="hidden  md:block"
      style={{
        background: token.colorBgContainer,
      }}
    >
      <div className="border-r-2">
        <div className={`flex items-center justify-center`}>
          {!collapsed ? (
            <div className="p-4 flex items-center ">
              <img src={assets.logoSrtgn} alt="logo" width={150} />
            </div>
          ) : (
            <div className="p-3">
              <img
                style={{ paddingTop: "2px" }}
                src={assets.logo}
                alt="logo"
                width={40}
                height={40}
              />
            </div>
          )}
        </div>
      </div>
      <Menu
        mode="inline"
        className={!collapsed ? "p-2" : ""}
        selectedKeys={[location.pathname]}
        // TODO : change it by finalFilteredItems
        items={finalFilteredItems}
        onClick={({ key }) => navigate(key)}
        style={{
          borderRight: "none",
          height: "89vh",
          overflowY: "scroll",
        }}
      />
    </Sider>
  );
};

export default AuthSidebar;