import { useEffect, useRef, useState } from "react";
import {
  Button,
  Modal,
  Form,
  Input,
  Popconfirm,
  Breadcrumb,
  Row,
  Col,
  Select,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";

import { useDispatch } from "react-redux";
import {
  deleteMotifDuplicate,
  getMotifDuplicates,
  storeMotifDuplicate,
  updateMotifDuplicate,
} from "../../../features/admin/motifDuplicateSlice.ts";
import { useSelector } from "react-redux";
import { getCardTypesAll } from "../../../features/admin/cardTypeSlice.ts";

function ManageDuplicateMotifs() {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;

  const actionRef: any = useRef<any>();
  const dispatch: any = useDispatch();

  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [editingDuplicateMotif, setEditingDuplicateMotif] = useState<any>(null);
  const [form] = Form.useForm();

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState(1);

  const cardsTypes = useSelector((state: any) => state.cardType.items.data);

  const fetchStoreData = async () => {
    if (!cardsTypes?.length) {
      await dispatch(getCardTypesAll()).unwrap();
    }
  };

  useEffect(() => {
    fetchStoreData();
  }, []);

  {
    /*|--------------------------------------------------------------------------
    | FETCH ALL motif duplicates WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
  }
  const handleGetMotifDuplicates = (params: any, sort: any, filter: any) =>
    dispatch(
      getMotifDuplicates({
        pageNumber,
        perPage: pageSize,
        params,
        sort,
        filter,
      })
    )
      .unwrap()
      .then((originalPromiseResult: any) => {
        setTotal(originalPromiseResult.total);
        return originalPromiseResult.data;
      })
      .catch((rejectedValueOrSerializedError: any) => {
        console.log(rejectedValueOrSerializedError);
      });

  /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
  const columns: any = [
    {
      title: t(`manage_duplicateMotifs.labels.name`),
      sorter: true,
      dataIndex: `nom_${currentLang}`,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => record[`nom_${currentLang}`],
    },
    {
      title: `${t("manage_duplicateMotifs.labels.cardType")}`,
      dataIndex: "id_card_type",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => record.card_type?.[`nom_${currentLang}`],
    },
    {
      title: `${t("manage_duplicateMotifs.labels.createdAt")}`,
      dataIndex: "created_at",
      valueType: "date",
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      sorter: true,
    },
    {
      title: `${t("manage_duplicateMotifs.labels.actions")}`,
      fixed: "right",
      width: 170,
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => (
        <div className="flex gap-1">
          <Button
            className="btn-view"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          {
            record?.id_card_type !== 1 && (
              <Button
                className="btn-edit"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            )
          }
          {
            record?.id_card_type !== 1 && (
               <Popconfirm
                title={t("manage_duplicateMotifs.confirmDelete")}
                onConfirm={() => handleDelete(record.id)}
                okText={t("manage_duplicateMotifs.yes")}
                cancelText={t("manage_duplicateMotifs.no")}
              >
                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
              </Popconfirm>
            )
          }
        </div>
      ),
    },
  ];
  const breadcrumbItems = [
    {
      title: (
        <Link className="!bg-white" to="/auth/commercial-dashboard">
          {t("auth_sidebar.categories.pricing")}
        </Link>
      ),
    },
    {
      title: t("manage_duplicateMotifs.title"),
    },
  ];

  /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
  const handleView: any = (record: any) => {
    setEditingDuplicateMotif(record);
    form.setFieldsValue(record);
    setViewMode(true);
    setModalVisible(true);
  };
  const handleEdit: any = (record: any) => {
    setEditingDuplicateMotif(record);
    form.setFieldsValue(record);
    setViewMode(false);
    setModalVisible(true);
  };
  const handleAdd: any = () => {
    setEditingDuplicateMotif(null);
    form.resetFields();
    setViewMode(false);
    setModalVisible(true);
  };

  /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE MOTIF DUPLICATE
    |-------------------------------------------------------------------------- */
  const handleFormSubmit: any = async (values: any) => {
    setLoading(true);
    const payload = editingDuplicateMotif
      ? { id: editingDuplicateMotif.id, ...values }
      : values;
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      if (editingDuplicateMotif) {
        await dispatch(updateMotifDuplicate(payload)).unwrap();
      } else {
        await dispatch(storeMotifDuplicate(values)).unwrap();
      }
      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
      handleReset();
    } catch (error: any) {
      const fieldErrors: any = Object.keys(error.errors || {}).map(
        (field: any) => ({
          name: field,
          errors: [error.errors[field][0]],
        })
      );
      form.setFields(fieldErrors);
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    } finally {
      setLoading(false);
    }
  };
  const confirmSubmit = (values: any) => {
    const modal = Modal.confirm({
      title: t("manage_duplicateMotifs.confirmAction"),
      content: editingDuplicateMotif
        ? t("manage_duplicateMotifs.confirmUpdate")
        : t("manage_duplicateMotifs.confirmAdd"),
      okText: t("common.yes"),
      cancelText: t("common.no"),
      onOk: async () => {
        modal.destroy();
        await handleFormSubmit(values);
      },
      centered: true,
    });
  };

  /*|--------------------------------------------------------------------------
    |  - DELETE MOTIF DUPLICATE
    |-------------------------------------------------------------------------- */
  const handleDelete: any = async (id: number) => {
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      await dispatch(deleteMotifDuplicate(id)).unwrap();
      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
    } catch (error: any) {
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    }
  };

  /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
  const handleReset = () => {
    setModalVisible(false);
    form.resetFields();
    setLoading(false);
  };

  return (
    <>
      <Breadcrumb className="mb-5" items={breadcrumbItems} />

      <Row>
        <Col span={24}>
          <ProTable
            headerTitle={t("manage_duplicateMotifs.title")}
            columns={columns}
            actionRef={actionRef}
            cardBordered
            rowKey={"id"}
            request={async (params: any, sort: any, filter: any) => {
              setLoading(true);
              const dataFilter: any = await handleGetMotifDuplicates(
                params,
                sort,
                filter
              );
              setLoading(false);
              return {
                data: dataFilter,
                success: true,
              };
            }}
            sortDirections={["ascend", "descend"]}
            pagination={{
              pageSize: pageSize,
              total: total,
              showSizeChanger: true,
              onChange: (page) => setPageNumber(page),
              onShowSizeChange: (_, pageSize) => {
                setPageSize(pageSize);
              },
            }}
            scroll={{
              x: 800,
            }}
            search={{
              labelWidth: "auto",
              className: "bg-[#FAFAFA]",
            }}
            options={{
              fullScreen: true,
            }}
            loading={loading}
            toolBarRender={() => [
              <Button key="button" onClick={handleAdd} className="btn-add">
                {t("manage_duplicateMotifs.add")}
              </Button>,
            ]}
          />
        </Col>
      </Row>

      <Modal
        width={900}
        centered={true}
        title={
          viewMode
            ? t("manage_duplicateMotifs.details")
            : editingDuplicateMotif
            ? t("manage_duplicateMotifs.edit")
            : t("manage_duplicateMotifs.add")
        }
        open={modalVisible}
        onCancel={() => handleReset()}
        onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
        okText={viewMode ? null : t("manage_duplicateMotifs.save")}
        footer={viewMode ? null : undefined}
        confirmLoading={loading}
      >
        <Form
          className="form-inputs"
          form={form}
          layout="vertical"
          onFinish={confirmSubmit}
          disabled={loading}
        >
          <Row gutter={16}>
            <Col xs={24} sm={8}>
              <Form.Item
                label={t("manage_duplicateMotifs.labels.name_fr")}
                name="nom_fr"
                rules={[
                  {
                    required: true,
                    message: t("manage_duplicateMotifs.errors.nameRequired"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_duplicateMotifs.placeholders.name_fr")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                label={t("manage_duplicateMotifs.labels.name_en")}
                name="nom_en"
                rules={[
                  {
                    required: true,
                    message: t("manage_duplicateMotifs.errors.nameRequired"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_duplicateMotifs.placeholders.name_en")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
                <Form.Item
                    label={t("manage_duplicateMotifs.labels.name_ar")}
                    name="nom_ar"
                    rules={[{ required: true, message: t("manage_duplicateMotifs.errors.nameRequired") }]}
                >
                    <Input
                        placeholder={t("manage_duplicateMotifs.placeholders.name_ar")}
                        disabled={viewMode}
                    />
                </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} sm={24}>
                <Form.Item
                    label={t("manage_assignAgents.labels.cardTypes")}
                    name="id_card_type"
                    rules={[{ required: true, message: t("manage_assignAgents.errors.cardTypesRequired") }]}
                >
                    <Select 
                        placeholder={t("manage_assignAgents.placeholders.cardTypes")} 
                        disabled={viewMode}
                    >
                        {cardsTypes?.map((cardType: any) => (
                            <Select.Option key={cardType.id} value={cardType.id}>
                                {cardType[`nom_${currentLang}`]}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
}

export default ManageDuplicateMotifs;
