import {useEffect, useRef, useState} from "react";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Col, Form, Input, Modal, Popconfirm, Row, Select, Spin, Tag,} from "antd";
import {DeleteOutlined, EditOutlined, EyeOutlined, LoadingOutlined} from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";

import LineDisplay from "../../../components/configs/LineDisplay.tsx";
import {useDispatch} from "react-redux";
import {deleteTrip, getTrips, storeTrip, updateTrip} from "../../../features/admin/tripsSlice.ts";
import {getTarifBasesAll} from "../../../features/admin/tarifBaseSlice.ts";
import {toast} from 'react-toastify';
import {getAbnTypesAll} from "../../../features/admin/abnTypeSlice.ts";
import {getLinesAll, getLineStationsAndRoutes} from "../../../features/admin/lineSlice.ts";
import { getStationsAll } from "../../../features/admin/stationSlice.ts";

function ManageRoutes() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;
    const dispatch: any = useDispatch();

    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingRoutes, setEditingRoutes] = useState<any>(null);
    const [form] = Form.useForm();


    const [stations, setStations] = useState<any[]>([]);
    const [selectedLine, setSelectedLine] = useState<any>(null);
    const [selectedStationDepart, setSelectedStationDepart] = useState(null);
    const [selectedStationArrival, setSelectedStationArrival] = useState(null);
    const [isReversed, setIsReversed] = useState(false);
    const [selectedABNType, setSelectedABNType] = useState<string[]>([]);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);
    const [baseTariffData, setBaseTariffData] = useState<any[]>([]);
    const [abnTypes, setAbnTypes] = useState<any[]>([]);
    const [lines, setLines] = useState<any[]>([]);

    /*|--------------------------------------------------------------------------
    | FETCH ALL ABN TYPES AND BASE TARIFF
    |-------------------------------------------------------------------------- */
    useEffect(() => {
        dispatch(getAbnTypesAll())
            .unwrap()
            .then((result: any) => {
                setAbnTypes(result.data);
            })
            .catch((error: any) => {
                console.error('Error fetching ABN types:', error);
                toast.error(t("messages.error"));
            });
    
        dispatch(getTarifBasesAll())
            .unwrap()
            .then((result: any) => {
                setBaseTariffData(result.data);
            })
            .catch((error: any) => {
                console.error('Error fetching tariff bases:', error);
                toast.error(t("messages.error"));
            });
    
        dispatch(getLinesAll())
            .unwrap()
            .then((result: any) => {
                setLines(result.data);
            })
            .catch((error: any) => {
                console.error('Error fetching lines:', error);
                toast.error(t("messages.error"));
            });

        dispatch(getStationsAll())
            .unwrap()
            .then((result: any) => {
                setStations(result.data);
            })
            .catch((error: any) => {
                console.error('Error fetching stations:', error);
                toast.error(t("messages.error"));
            });
    }, []);

    const handleLineChange = async (lineId: number) => {
        setLoading(true)
        try {
            const selectedLineData = await dispatch(getLineStationsAndRoutes(lineId)).unwrap();
            setSelectedLine(selectedLineData);
            
        } catch (error) {
            console.error('Error fetching line details:', error);
            toast.error(t("messages.error"));
        } finally {
            setLoading(false)
        }
    };

    /*|--------------------------------------------------------------------------
    | FETCH TRIPS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetTrips = (params: any, sort: any, filter: any) => {
        return dispatch(getTrips({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t("manage_routes.labels.name"),
            dataIndex: `nom_${currentLang}`,
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: t("manage_routes.labels.station_depart"),
            dataIndex: "station_start",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => {
                return data.station_start ? data.station_start[`nom_${currentLang}`] : '-';
            },
            renderFormItem: () => (
                <Select
                    placeholder={t("manage_routes.placeholders.station_depart")}
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    options={stations?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                />
            ),
        },
        {
            title: t("manage_routes.labels.station_arrival"),
            dataIndex: "station_end",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => {
                return data.station_end ? data.station_end[`nom_${currentLang}`] : '-';
            },
            renderFormItem: () => (
                <Select
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    placeholder={t("manage_routes.placeholders.station_arrival")}
                    options={stations?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                />
            ),
        },
        {
            title: t("manage_routes.labels.numberOfKm"),
            dataIndex: 'number_of_km',
            key: 'number_of_km',
            sorter: true,
            render: (text: any) => `${text} km`
        },
        {
            title: t("manage_routes.labels.regular"),
            dataIndex: "is_regular",
            search: false,
            hidden: true,
            render: (is_regular:any) => (
                <Tag color={is_regular ? "green" : "red"}>{is_regular ? t("manage_routes.options_regular.yes") : t("manage_routes.options_regular.no") }</Tag>
            ),
            renderFormItem: () => (
                <Select placeholder={t("manage_routes.filters.regular")}>
                    <Select.Option value="TRUE">{t("manage_routes.options_regular.yes")}</Select.Option>
                    <Select.Option value="FALSE">{t("manage_routes.options_regular.no")}</Select.Option>
                </Select>
            ),
        },
        {
            title: t("manage_routes.labels.status"),
            dataIndex: "status",
            width: 100,
            responsive: ["xs", "sm", "md", "lg"],
            valueType: "select",
            render: (_: any, record: any) => (
                <Tag color={record.status === true ? 'success' : 'error'}>
                    {record.status === true ? t("common.active") : t("common.inactive")}
                </Tag>
            ),
            valueEnum: {
                "1": { 
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": { 
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: t("manage_routes.labels.createdAt"),
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("manage_routes.labels.actions"),
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button 
                        className="btn-view" 
                        icon={<EyeOutlined />} 
                        onClick={() => handleOpenModal(record, true)} 
                    />
                    <Button 
                        className="btn-edit" 
                        icon={<EditOutlined />} 
                        onClick={() => handleOpenModal(record, false)} 
                    />
                    <Popconfirm
                        title={t("manage_routes.confirmAction")}
                        description={t("manage_routes.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_routes.yes")}
                        cancelText={t("manage_routes.no")}
                        placement="topRight"
                    >
                        <Button 
                            className="btn-delete" 
                            icon={<DeleteOutlined />} 
                            danger 
                        />
                    </Popconfirm>
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.transport")}</Link>,
        },
        {
            title: t("manage_routes.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleAdd = () => {
        setEditingRoutes(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
        setSelectedABNType([])
    }
    const handleOpenModal = (record: any, isViewMode: boolean) => {
        setEditingRoutes(record);
        setViewMode(isViewMode);

        const formValues: any = {
            nom_fr: record.nom_fr,
            nom_ar: record.nom_ar,
            nom_en: record.nom_en,
            status: record.status,
            number_of_km: record.number_of_km,
            line: record.line?.id,
        };

        if (record.tariff_options && Array.isArray(record.tariff_options)) {
            formValues.tariff_types = record.tariff_options.reduce((acc: any, curr: any) => {
                const key = curr.id_subs_type;
                acc[key] = {
                    is_regular: curr.is_regular,
                    type_abn_id: curr.id_subs_type,
                    tariff: curr.is_regular ? curr.id_tariff_base : curr.manual_tariff
                };
                return acc;
            }, {});

            const selectedTypes = record.tariff_options?.map((t: any) => t.id_subs_type);
            formValues.abnType = selectedTypes;
            setSelectedABNType(selectedTypes);
        }

        if (record.station_start) {
            formValues.station_start = record.station_start.id;
            setSelectedStationDepart(record.station_start.id);
        }

        if (record.station_end) {
            formValues.station_end = record.station_end.id;
            setSelectedStationArrival(record.station_end.id);
        }

        if (record.line?.id) {
            handleLineChange(record.line.id);
        }

        form.setFieldsValue(formValues);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE ROUTE
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            const stations = {
                id_station_start: values.station_start,
                id_station_end: values.station_end
            };

            const tariff_options: any = Object.entries(values.tariff_types)?.map(([typeId, data]: [string, any]) => ({
                id_subs_type: Number(typeId),
                is_regular: data.is_regular,
                ...(data.is_regular
                    ? { id_tariff_base: data.tariff }
                    : { manual_tariff: data.tariff })
            }));

            const payload = {
                nom_fr: values.nom_fr,
                nom_ar: values.nom_ar,
                nom_en: values.nom_en,
                number_of_km: Number(values.number_of_km),
                status: values.status,
                id_line: values.line,
                stations,
                tariff_options,
                inter_station: false
            };

            if (editingRoutes) {
                await dispatch(updateTrip({ id: editingRoutes.id, ...payload })).unwrap();
            } else {
                await dispatch(storeTrip(payload)).unwrap();
            }

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages."+error.message) ||t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit:any = (values: any) => {
        const modal:any = Modal.confirm({
            title: t("manage_routes.confirmAction"),
            content: editingRoutes
                ? t("manage_routes.confirmUpdate")
                : t("manage_routes.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE ROUTE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteTrip(id)).unwrap();
            
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages."+error.message) || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setViewMode(false);
        setSelectedABNType([]);
        setSelectedLine(null);
        setSelectedStationDepart(null);
        setSelectedStationArrival(null);
        setIsReversed(false);
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_routes.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetTrips(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        sortDirections={["ascend", "descend"]}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_routes.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={1200}
                title={viewMode ? t("manage_routes.view") : editingRoutes ? t("manage_routes.edit") : t("manage_routes.add")}
                open={modalVisible}
                onCancel={handleReset}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_routes.save")}
                footer={viewMode ? null : undefined}
                destroyOnClose
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                label={t("manage_routes.labels.abnType")}
                                name="abnType"
                                rules={[{ required: true, message: t("manage_routes.errors.abnTypeRequired") }]}
                            >
                                <Select
                                    disabled={viewMode || loading}
                                    mode="multiple"
                                    loading={loading}
                                    placeholder={t("manage_routes.placeholders.abnType")}
                                    onChange={setSelectedABNType}
                                >
                                    {abnTypes.map((el) => (
                                         <Select.Option key={el.id} value={el.id}>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                <div
                                                    style={{
                                                        width: '12px',
                                                        height: '12px',
                                                        borderRadius: '50%',
                                                        backgroundColor: el.color
                                                    }}
                                                />
                                                {el[`nom_${currentLang}`]}
                                            </div>
                                     </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    {selectedABNType && selectedABNType.length > 0 && (
                        <>
                            {selectedABNType.map((abnTypeId) => {
                                const key:any = String(abnTypeId);
                                const abnType:any = abnTypes.find((item:any) => item.id === abnTypeId);
                                const baseTariffsForType = baseTariffData.filter(
                                    (tariff: any) => tariff.subs_type.id === abnTypeId
                                );

                                return (
                                    <Row gutter={[16, 16]} key={abnTypeId}>
                                        <Col xs={24} sm={12}>
                                            <Form.Item
                                                label={`${t("manage_routes.labels.regular")} (${abnType[`nom_${currentLang}`]})`}
                                                name={["tariff_types", key, "is_regular"]}
                                                rules={[{ required: true, message: t("manage_routes.errors.isRegularRequired") }]}
                                            >
                                                <Select
                                                    disabled={viewMode}
                                                    placeholder={t("manage_routes.placeholders.regular")}
                                                    onChange={() => {
                                                        form.setFieldsValue({
                                                            tariff_types: {
                                                                [key]: {
                                                                    tariff: null
                                                                }
                                                            }
                                                        });
                                                    }}
                                                >
                                                    <Select.Option value={true}>
                                                        {t("manage_routes.options_regular.yes")}
                                                    </Select.Option>
                                                    <Select.Option value={false}>
                                                        {t("manage_routes.options_regular.no")}
                                                    </Select.Option>
                                                </Select>
                                            </Form.Item>

                                            <Form.Item
                                                name={["tariff_types", key, "type_abn_id"]}
                                                initialValue={abnTypeId}
                                                style={{ display: "none" }}
                                            >
                                                <Input type="hidden" />
                                            </Form.Item>
                                        </Col>

                                        <Col xs={24} sm={12}>
                                            <Form.Item
                                                noStyle
                                                shouldUpdate={(prevValues, curValues) =>
                                                    prevValues?.tariff_types?.[key]?.is_regular !==
                                                    curValues?.tariff_types?.[key]?.is_regular
                                                }
                                            >
                                                {({ getFieldValue }) => {
                                                    const isRegular = getFieldValue(["tariff_types", key, "is_regular"]);
                                                    if (isRegular === true) {
                                                        return (
                                                            <Form.Item
                                                                key={`base-${key}-${isRegular}`}
                                                                label={`${t("manage_routes.labels.base_tariff")} (${abnType[`nom_${currentLang}`]})`}
                                                                name={["tariff_types", key, "tariff"]}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: t("manage_routes.errors.baseTariffRequired"),
                                                                    },
                                                                ]}
                                                            >
                                                                <Select
                                                                    disabled={viewMode}
                                                                    placeholder={t("manage_routes.placeholders.base_tariff")}
                                                                >
                                                                    {baseTariffsForType.map((el:any) => (
                                                                        <Select.Option key={el.id} value={el.id}>
                                                                            {el[`nom_${currentLang}`]} - <small>{el.tariffPerKM} TND</small>
                                                                        </Select.Option>
                                                                    ))}
                                                                </Select>
                                                            </Form.Item>
                                                        );
                                                    } else if (isRegular === false) {
                                                        return (
                                                            <Form.Item
                                                                key={`manual-${key}-${isRegular}`}
                                                                label={`${t("manage_routes.labels.manual_tariff")} (${abnType[`nom_${currentLang}`]})`}
                                                                name={["tariff_types", key, "tariff"]}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: t("manage_routes.errors.manualTariffRequired"),
                                                                    },
                                                                ]}
                                                            >
                                                                <Input
                                                                    min={0}
                                                                    type="number"
                                                                    placeholder={t("manage_routes.placeholders.manual_tariff")}
                                                                    disabled={viewMode}
                                                                />
                                                            </Form.Item>
                                                        );
                                                    }
                                                    return null;
                                                }}
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                );
                            })}
                        </>
                    )}

                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_routes.labels.numberOfKm")}
                                name="number_of_km"
                                rules={[{ required: true, message: t("manage_routes.errors.numberOfKmRequired") }]}
                            >
                                <Input
                                    type="number"
                                    placeholder={t("manage_routes.placeholders.numberOfKm")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                        <Form.Item
                                name="status"
                                label={t("manage_routes.labels.status")}
                                initialValue={true}
                                rules={[{ required: true, message: t("common.required") }]}
                            >
                                <Select 
                                    disabled={viewMode}
                                    options={[
                                        { label: t("common.active"), value: true },
                                        { label: t("common.inactive"), value: false }
                                    ]}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <div className="mt-2 mb-4 font-semibold bg-gray-100 p-4 text-center">
                        <h3>{t("manage_routes.select_depart_arrival")}</h3>
                    </div>

                    <Col xs={24} sm={24}>
                        <Form.Item
                            label={t("manage_routes.labels.line")}
                            name="line"
                            rules={[{ required: true, message: t("manage_routes.errors.lineRequired") }]}
                        >
                            <Select
                                disabled={viewMode || loading}
                                placeholder={t("manage_routes.placeholders.line")}
                                onChange={handleLineChange}
                                loading={loading}
                            >
                                {lines.map((line) => (
                                    <Select.Option key={line.id} value={line.id}>
                                        {line.CODE_LINE} - {line[`nom_${currentLang}`]}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    
                    <Spin
                        spinning={loading}
                        indicator={
                            <LoadingOutlined
                                style={{
                                    fontSize: 24,
                                    color: 'var(--primary-color)'
                                }}
                                spin
                            />
                        }
                    >
                        {selectedLine && (
                            <div className="flex flex-col pt-6 pb-12">
                                <LineDisplay
                                    isReversed={isReversed}
                                    record={selectedLine}
                                    selectedStationDepart={selectedStationDepart}
                                    selectedStationArrival={selectedStationArrival}
                                />
                            </div>
                        )}
                    </Spin>
                    
                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={12}>
                            {selectedLine && (
                                <Form.Item
                                    label={t("manage_routes.labels.station_depart")}
                                    name="station_start"
                                    rules={[{ required: true, message: t("manage_routes.errors.stationDepartRequired") }]}
                                >
                                    <Select
                                        disabled={viewMode}
                                        placeholder={t("manage_routes.placeholders.station_depart")}
                                        onChange={setSelectedStationDepart}
                                    >
                                        {selectedLine.stations?.map((station: any) => (
                                            <Select.Option key={station.id} value={station.id}>
                                                {station[`nom_${currentLang}`]}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            )}
                        </Col>
                        <Col xs={24} sm={12}>
                            {selectedLine && (
                                <Form.Item
                                    label={t("manage_routes.labels.station_arrival")}
                                    name="station_end"
                                    rules={[{ required: true, message: t("manage_routes.errors.stationArrivalRequired") }]}
                                >
                                    <Select
                                        disabled={viewMode}
                                        placeholder={t("manage_routes.placeholders.station_arrival")}
                                        onChange={setSelectedStationArrival}
                                    >
                                        {selectedLine.stations?.map((station: any) => (
                                            <Select.Option key={station.id} value={station.id}>
                                                {station[`nom_${currentLang}`]}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            )}
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageRoutes;
